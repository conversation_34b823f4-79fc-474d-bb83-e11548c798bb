["\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\tray\\autogenerated\\default.toml"]