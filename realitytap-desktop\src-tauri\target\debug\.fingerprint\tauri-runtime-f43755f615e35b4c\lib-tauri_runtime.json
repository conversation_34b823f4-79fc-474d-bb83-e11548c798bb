{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 17452753421783509941, "deps": [[442785307232013896, "build_script_build", false, 16776603775144676740], [3150220818285335163, "url", false, 5408171043244425519], [4143744114649553716, "raw_window_handle", false, 8959467065406847557], [7606335748176206944, "dpi", false, 2926432648544458087], [9010263965687315507, "http", false, 16950085000799907330], [9689903380558560274, "serde", false, 3282804803960374299], [10806645703491011684, "thiserror", false, 9617457741875845117], [11050281405049894993, "tauri_utils", false, 4988308089169363450], [13116089016666501665, "windows", false, 12387249425082537729], [15367738274754116744, "serde_json", false, 15916909028635074265], [16727543399706004146, "cookie", false, 4088740567826924174]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-f43755f615e35b4c\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}