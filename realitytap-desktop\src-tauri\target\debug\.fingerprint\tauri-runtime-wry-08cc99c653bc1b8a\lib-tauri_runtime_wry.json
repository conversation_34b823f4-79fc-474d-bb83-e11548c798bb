{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 3349273884199032985, "deps": [[376837177317575824, "softbuffer", false, 15930003646028465403], [442785307232013896, "tauri_runtime", false, 18422432015645049330], [3150220818285335163, "url", false, 5408171043244425519], [3722963349756955755, "once_cell", false, 11521336827388356010], [4143744114649553716, "raw_window_handle", false, 8959467065406847557], [5986029879202738730, "log", false, 15354736063082002461], [7752760652095876438, "build_script_build", false, 9286779516598144398], [8539587424388551196, "webview2_com", false, 8220371095569886386], [9010263965687315507, "http", false, 16950085000799907330], [11050281405049894993, "tauri_utils", false, 4988308089169363450], [13116089016666501665, "windows", false, 12387249425082537729], [13223659721939363523, "tao", false, 11987982013079462361], [14794439852947137341, "wry", false, 16201299386623101311]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-08cc99c653bc1b8a\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}