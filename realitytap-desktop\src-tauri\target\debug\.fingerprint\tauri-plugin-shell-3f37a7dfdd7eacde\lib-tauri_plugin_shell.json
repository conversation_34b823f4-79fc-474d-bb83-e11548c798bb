{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 9836949865237587686, "deps": [[500211409582349667, "shared_child", false, 11644816114514584771], [1582828171158827377, "build_script_build", false, 4537830506992277775], [5138218615291878843, "tokio", false, 15250874349822087297], [5986029879202738730, "log", false, 15354736063082002461], [9451456094439810778, "regex", false, 13504694695571801301], [9689903380558560274, "serde", false, 3282804803960374299], [10755362358622467486, "tauri", false, 18403606794473902224], [10806645703491011684, "thiserror", false, 9617457741875845117], [11337703028400419576, "os_pipe", false, 1936321653756125695], [14564311161534545801, "encoding_rs", false, 1677286647306383756], [15367738274754116744, "serde_json", false, 15916909028635074265], [16192041687293812804, "open", false, 2526121153992779627]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-3f37a7dfdd7eacde\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}