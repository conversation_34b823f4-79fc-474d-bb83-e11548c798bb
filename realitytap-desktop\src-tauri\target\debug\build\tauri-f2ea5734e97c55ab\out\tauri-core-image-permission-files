["\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\image\\autogenerated\\commands\\from_bytes.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\image\\autogenerated\\commands\\from_path.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\image\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\image\\autogenerated\\commands\\rgba.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\image\\autogenerated\\commands\\size.toml", "\\\\?\\E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\tauri-f2ea5734e97c55ab\\out\\permissions\\image\\autogenerated\\default.toml"]