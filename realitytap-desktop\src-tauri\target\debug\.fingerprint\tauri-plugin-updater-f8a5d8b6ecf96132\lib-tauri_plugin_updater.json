{"rustc": 10895048813736897673, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 15657897354478470176, "path": 1006655914083043434, "deps": [[40386456601120721, "percent_encoding", false, 11430224217469457874], [1441306149310335789, "tempfile", false, 6265381824366887658], [3150220818285335163, "url", false, 5408171043244425519], [4899080583175475170, "semver", false, 9299938287309075248], [5138218615291878843, "tokio", false, 15250874349822087297], [5986029879202738730, "log", false, 15354736063082002461], [9010263965687315507, "http", false, 16950085000799907330], [9332307739160395223, "minisign_verify", false, 432342414700116068], [9689903380558560274, "serde", false, 3282804803960374299], [10281541584571964250, "windows_sys", false, 6189054128043710882], [10629569228670356391, "futures_util", false, 757868430050288205], [10755362358622467486, "tauri", false, 18403606794473902224], [10806645703491011684, "thiserror", false, 9617457741875845117], [11721252211900136025, "build_script_build", false, 15753928962352390832], [11727988951467706918, "zip", false, 1830740571389050989], [12409575957772518135, "time", false, 835033091885444008], [13077212702700853852, "base64", false, 10972977749164998151], [15367738274754116744, "serde_json", false, 15916909028635074265], [16593743942913858012, "reqwest", false, 16019467607456567450], [17146114186171651583, "infer", false, 772951795503138815]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-f8a5d8b6ecf96132\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}